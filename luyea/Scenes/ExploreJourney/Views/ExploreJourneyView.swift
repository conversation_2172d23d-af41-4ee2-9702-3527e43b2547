import SwiftUI



/// 探索旅途主视图
///
/// 应用的主要探索页面，提供旅游内容的浏览和发现功能。
/// 包含发现和附近两个标签页，支持话题筛选和目的地选择。
///
/// 主要功能：
/// - 发现页面：展示推荐的旅游内容和景点
/// - 附近页面：显示基于位置的推荐内容
/// - 话题筛选：支持按兴趣话题筛选内容
/// - 目的地选择：支持选择特定目的地进行筛选
/// - 内容详情：支持导航到具体内容的详情页面
///
/// 架构特点：
/// - 采用 MVVM 架构模式，分离业务逻辑和视图
/// - 使用组件化设计，功能模块独立管理
/// - 集成全局背景样式系统
/// - 响应式 TabBar 显示控制
/// - 支持异步数据加载和状态管理
struct ExploreJourneyView: View {

    // MARK: - UI State

    /// 当前选中的标签页（发现/附近）
    @State private var selectedTab: DiscoverJourneyTab = .discover

    /// 目的地选择弹窗是否显示
    @State private var showDestinationPicker = false

    /// 顶部定位/精选按钮当前显示内容
    @State private var selectedLocation: String = "精选"

    /// 导航到详情页的状态
    @State private var selectedDetailParams: DiscoverDetailModels.NavigationParams?



    // MARK: - Environment & ViewModels



    /// 探索旅途视图模型
    @StateObject private var viewModel = ExploreJourneyViewModel()

    /// 发现页面视图模型（现在也管理话题数据）
    @StateObject private var discoverViewModel = DiscoverViewModel()

    /// 周边页面视图模型（保持状态）
    @StateObject private var nearbyPreferenceViewModel = TravelPreferenceViewModel()

    // MARK: - Computed Properties

    var body: some View {
        NavigationStack {
            // 主内容层 - 包含所有页面内容
            mainContentLayer
                .coordinateSpace(name: "ExploreJourneyView")
            .navigationTitle("")
            .navigationDestination(item: $selectedDetailParams) { params in
                DiscoverDetailView(navigationParams: params)
            }
            .showTabBar()
        }
        .appBackground() // 使用全局背景样式
        .sheet(isPresented: $showDestinationPicker) {
            destinationPickerSheet
        }
        .task {
            await initializeData()
        }
        .onChange(of: discoverViewModel.selectedTopicIds) { _, newValue in
            handleTopicSelectionChange(newValue)
        }
    }

    // MARK: - Layer Components

    /// 主内容层 - 包含导航栏、筛选栏和页面内容
    private var mainContentLayer: some View {
        VStack(spacing: 0) {
            // 固定顶部区域
            topFixedArea

            // 可滚动内容区域
            contentArea
        }
    }



    // MARK: - Content Sections

    /// 顶部固定区域 - 包含导航栏和筛选栏
    private var topFixedArea: some View {
        VStack(spacing: 0) {
            // 使用新的导航栏组件
            ExploreNavigationBar(
                selectedTab: $selectedTab,
                onTabChange: handleTabChange
            )
            .background(Color(.systemBackground))
            .shadow(
                color: Color.primary.opacity(ExploreJourneyConstants.Shadow.shadowOpacity),
                radius: ExploreJourneyConstants.Shadow.shadowRadius,
                x: 0,
                y: ExploreJourneyConstants.Shadow.shadowOffset
            )
            .zIndex(2)

            if selectedTab == .discover {
                // 使用新的筛选栏组件
                DiscoverFilterBarView(
                    exploreViewModel: viewModel,
                    discoverViewModel: discoverViewModel,
                    selectedLocation: selectedLocation,
                    onDestinationButtonTap: handleDestinationButtonTap
                )
                .background(Color(.systemBackground))
                .shadow(
                    color: Color.primary.opacity(ExploreJourneyConstants.Shadow.shadowOpacity),
                    radius: ExploreJourneyConstants.Shadow.shadowRadius,
                    x: 0,
                    y: ExploreJourneyConstants.Shadow.shadowOffset
                )
                .zIndex(2)
                .transition(.move(edge: .top).combined(with: .opacity))
                .animation(.easeInOut(duration: DesignSystemConstants.Animation.tabSwitch), value: selectedTab)
            }
        }
    }

    /// 内容区域 - 使用新的功能容器视图（优化版本）
    private var contentArea: some View {
        ZStack {
            // 发现页面 - 始终保持在内存中，只控制可见性
            DiscoverView(
                discoverViewModel: discoverViewModel,
                exploreViewModel: viewModel,
                selectedDetailParams: $selectedDetailParams
            )
            .opacity(selectedTab == .discover ? 1 : 0)
            .allowsHitTesting(selectedTab == .discover)
            .zIndex(selectedTab == .discover ? 1 : 0)

            // 周边页面 - 始终保持在内存中，只控制可见性
            NearbyView(
                preferenceViewModel: nearbyPreferenceViewModel,
                exploreViewModel: viewModel,
                selectedNearbyItem: .constant(nil)
            )
            .opacity(selectedTab == .nearby ? 1 : 0)
            .allowsHitTesting(selectedTab == .nearby)
            .zIndex(selectedTab == .nearby ? 1 : 0)
        }
        .ignoresSafeArea() // 让内容延伸到安全区域，但会被 safeAreaInset 正确处理
    }

    /// 目的地选择弹窗
    private var destinationPickerSheet: some View {
        DestinationPickerView(
            viewModel: discoverViewModel,
            isPresented: $showDestinationPicker,
            selectedLocation: $selectedLocation
        )
        .presentationDetents([.medium])
        .presentationDragIndicator(.visible)
    }

    // MARK: - Data & Event Handlers

    /// 初始化数据
    private func initializeData() async {
        // DiscoverViewModel在初始化时已经自动加载话题数据，这里不需要额外操作
    }

    /// 处理话题选择变化
    private func handleTopicSelectionChange(_ newValue: Set<String>) {
        // 同步到ExploreJourneyViewModel（用于UI状态管理）
        viewModel.selectedTopicIds = newValue

        // DiscoverViewModel已经直接管理选中状态，不需要额外同步
    }

    // MARK: - Action Handlers

    /// 处理Tab切换
    private func handleTabChange(_ newTab: DiscoverJourneyTab) {
        selectedTab = newTab

        // 隐藏话题抽屉（如果显示的话）
        if viewModel.isTopicDrawerShowing {
            viewModel.hideTopicDrawer()
        }

        // 发送Tab切换通知
        NotificationCenter.default.post(
            name: NSNotification.Name("TabDidChange"),
            object: nil
        )
    }

    /// 处理目的地按钮点击
    private func handleDestinationButtonTap() {
        viewModel.hideTopicDrawer()
        withAnimation(.easeInOut(duration: DesignSystemConstants.Animation.tabSwitch)) {
            showDestinationPicker = true
        }
    }
}

enum DiscoverJourneyTab: String, CaseIterable, Identifiable {
    case discover = "发现"
    case nearby = "周边"
    
    var id: String { self.rawValue }
}

#Preview {
    ExploreJourneyView()

} 
