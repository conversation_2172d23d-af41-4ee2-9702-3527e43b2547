import SwiftUI

/// 发现页卡片组件
struct DiscoverCardView: View {
    let item: DiscoverItem
    @Binding var selectedTopicIds: Set<String>
    let onTopicSelected: (Set<String>) -> Void
    let onCardTapped: (DiscoverItem) -> Void

    var body: some View {
        ZStack(alignment: .topTrailing) {
            // 主卡片内容
            DiscoverItemView(
                item: item,
                onTagPressed: nil,
                onCardTapped: {
                    onCardTapped(item)
                }
            )

            // 话题标签（悬浮层）
            if let topic = item.topic {
                topicTagButton(for: topic)
            }
        }
    }

    @ViewBuilder
    private func topicTagButton(for topic: Topic) -> some View {
        Button(action: {
            // 检查话题是否已经被选中
            if !selectedTopicIds.contains(topic.id) {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    // 只有当话题未被选中时才添加并触发回调
                    var newSelectedTopics = selectedTopicIds
                    newSelectedTopics.insert(topic.id)
                    onTopicSelected(newSelectedTopics)
                }
            }
            // 如果话题已经被选中，则不执行任何操作
        }) {
            TopicTagView(topic: topic, style: .display)
        }
        .padding(.top, 8)
        .padding(.trailing, 8)
        .padding(.leading, 4)
        .padding(.bottom, 4)
        .contentShape(Rectangle())
        .allowsHitTesting(true)
        .zIndex(1)
    }
}

#Preview {
    DiscoverCardView(
        item: DiscoverItem(
            id: "1",
            imageUrls: ["https://example.com/image.jpg"],
            title: "示例标题",
            username: "用户名",
            userAvatarUrl: "https://example.com/avatar.jpg",
            likes: 100,
            description: "示例描述",
            location: "示例位置",
            comments: [],
            topic: Topic(id: "1", name: "示例话题", order: 1)
        ),
        selectedTopicIds: .constant([]),
        onTopicSelected: { _ in },
        onCardTapped: { _ in }
    )
    .padding()
    .background(Color(.systemGroupedBackground))
}
