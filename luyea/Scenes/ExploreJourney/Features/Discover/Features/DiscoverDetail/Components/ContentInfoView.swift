import SwiftUI

// MARK: - ContentInfoView

/// 内容信息展示组件
///
/// **功能概述:**
/// - 展示发现内容的标题、描述、位置和话题信息
/// - 采用现代化的卡片式设计，支持话题标签和位置标签
/// - 响应式布局，适配不同屏幕尺寸
///
/// **设计特色:**
/// - 胶囊式话题标签，带有图标和边框效果
/// - 位置信息标签，简洁明了
/// - 优化的文字排版和间距
/// - 支持多行文本显示
struct ContentInfoView: View {

    // MARK: - Properties

    /// 内容标题
    let title: String
    /// 内容描述
    let description: String
    /// 位置信息
    let location: String
    /// 话题信息
    let topic: Topic

    // MARK: - Body

    var body: some View {
        VStack(alignment: .leading, spacing: 18) { // 增加标题和描述间距从16到18
            // 标题和标签区域
            titleSection
                .animation(.easeOut(duration: 0.3), value: title)

            // 描述区域
            if !description.isEmpty {
                descriptionSection
                    .transition(.opacity.combined(with: .move(edge: .top)))
                    .animation(.easeOut(duration: 0.4).delay(0.1), value: description)
            }
        }
        .padding(.horizontal, 20)
        .onAppear {
            // 添加入场动画
            withAnimation(.easeOut(duration: 0.5)) {
                // 触发动画
            }
        }
    }

    // MARK: - Component Views

    /// 标题区域组件
    private var titleSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 主标题
            titleText

            // 标签行（话题 + 位置）
            tagRow
        }
    }

    /// 标题文本（与内容大小一致，仅加粗区分）
    private var titleText: some View {
        Text(title)
            .font(.callout) // 与描述内容使用相同的字体大小
            .fontWeight(.bold) // 通过加粗来区分标题和内容
            .foregroundColor(.primary)
            .multilineTextAlignment(.leading) // 多行文本左对齐
            .lineLimit(nil) // 允许多行显示
            .fixedSize(horizontal: false, vertical: true) // 垂直方向自适应，水平方向占满可用宽度
            .frame(maxWidth: .infinity, alignment: .leading)
    }

    /// 标签行（话题和位置标签）
    private var tagRow: some View {
        HStack(spacing: 8) {
            // 话题标签
            topicTag

            // 位置标签（如果有位置信息）
            if !location.isEmpty {
                locationTag
            }

            Spacer()
        }
    }

    /// 话题标签（胶囊式设计，带图标和交互效果）
    private var topicTag: some View {
        Button(action: {
            // TODO: 导航到话题页面
            print("点击话题: \(topic.name)")
        }) {
            HStack(spacing: 4) {
                Image(systemName: "tag.fill")
                    .font(.caption2)
                    .foregroundColor(.blue)

                Text(topic.name)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.blue)
            }
            .padding(.horizontal, 10)
            .padding(.vertical, 6)
            .background(
                Capsule()
                    .fill(Color.blue.opacity(0.10)) // 稍微降低背景透明度，更加精致
                    .overlay(
                        Capsule()
                            .stroke(Color.blue.opacity(0.25), lineWidth: 0.5) // 降低边框透明度
                    )
            )
        }
        .buttonStyle(ScaleButtonStyle())
    }

    /// 位置标签
    private var locationTag: some View {
        HStack(spacing: 4) {
            Image(systemName: "location")
                .font(.caption)
                .foregroundColor(.secondary)

            Text(location)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            Capsule()
                .fill(Color(.systemGray6))
        )
    }

    /// 描述区域（调整为适中大小）
    private var descriptionSection: some View {
        Text(description)
            .font(.callout) // 从.body调整为.callout，稍小一些更加合适
            .foregroundColor(.primary)
            .lineLimit(nil)
            .lineSpacing(4) // 调整行间距为4，与字体大小匹配
            .multilineTextAlignment(.leading)
            .frame(maxWidth: .infinity, alignment: .leading)
    }

}

// MARK: - Preview

#Preview("标准内容") {
    ContentInfoView(
        title: "美丽的自然风光探索之旅",
        description: "这是一个非常美丽的地方，有着壮观的山景和清澈的湖水。这里的空气清新，环境优美，是一个非常适合旅游和放松的好地方。在这里你可以感受到大自然的魅力，体验到前所未有的宁静与美好。",
        location: "九寨沟国家级自然保护区",
        topic: Topic(id: "1", name: "自然风光", order: 1)
    )
    .padding()
    .background(Color(.systemBackground))
}

#Preview("无位置信息") {
    ContentInfoView(
        title: "城市探索之旅",
        description: "在这个繁华的都市中，每一个角落都充满了惊喜和故事。现代建筑与传统文化的完美融合，创造出独特的城市魅力。",
        location: "",
        topic: Topic(id: "2", name: "城市探索", order: 2)
    )
    .padding()
    .background(Color(.systemBackground))
}

#Preview("长标题测试") {
    ContentInfoView(
        title: "这是一个非常长的标题，用来测试多行文本的显示效果和布局适应性，确保在各种设备上都能正常显示",
        description: "详细的描述内容，包含了很多有趣的信息和细节，让读者能够更好地了解这个地方的特色和魅力。这里有丰富的历史文化底蕴，独特的地理环境，以及热情好客的当地人民。",
        location: "某个很长的地名用来测试布局适应性",
        topic: Topic(id: "3", name: "文化体验", order: 3)
    )
    .padding()
    .background(Color(.systemBackground))
}
