import SwiftUI

/// 评论视图组件
/// 
/// 用于显示单条评论的内容，包括用户头像、用户名和评论文本。
/// 这是一个可复用的组件，遵循单一职责原则。
struct CommentView: View {
    
    // MARK: - Properties
    
    /// 评论数据
    let comment: DiscoverDetailModels.Comment
    
    // MARK: - Body
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(alignment: .top, spacing: 12) {
                // 用户头像
                userAvatar
                
                // 评论内容
                commentContent
                
                Spacer()
            }
        }
        .padding(.vertical, 8)
    }
    
    // MARK: - Private Views
    
    /// 用户头像视图
    private var userAvatar: some View {
        Circle()
            .fill(Color.gray.opacity(0.2))
            .frame(width: 32, height: 32)
            .overlay(
                Text(String(comment.author.username.prefix(1)))
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.gray)
            )
    }
    
    /// 评论内容视图
    private var commentContent: some View {
        VStack(alignment: .leading, spacing: 4) {
            // 用户名
            Text(comment.author.username)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            // 评论文本
            Text(comment.content)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .lineLimit(nil)
        }
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 16) {
        CommentView(
            comment: DiscoverDetailModels.Comment(
                id: "1",
                content: "这是一条很棒的评论！",
                author: DiscoverDetailModels.CommentAuthor(
                    id: "user1",
                    username: "张三",
                    avatarUrl: nil,
                    isVerified: false
                ),
                createdAt: Date(),
                likeCount: 5,
                isLiked: false,
                replies: []
            )
        )

        CommentView(
            comment: DiscoverDetailModels.Comment(
                id: "2",
                content: "这是一条比较长的评论，用来测试多行文本的显示效果。这里有更多的内容来展示评论的完整布局。",
                author: DiscoverDetailModels.CommentAuthor(
                    id: "user2",
                    username: "李四",
                    avatarUrl: nil,
                    isVerified: true
                ),
                createdAt: Date(),
                likeCount: 12,
                isLiked: true,
                replies: []
            )
        )

        CommentView(
            comment: DiscoverDetailModels.Comment(
                id: "3",
                content: "简短评论",
                author: DiscoverDetailModels.CommentAuthor(
                    id: "user3",
                    username: "王五",
                    avatarUrl: nil,
                    isVerified: false
                ),
                createdAt: Date(),
                likeCount: 0,
                isLiked: false,
                replies: []
            )
        )
    }
    .padding()
}
