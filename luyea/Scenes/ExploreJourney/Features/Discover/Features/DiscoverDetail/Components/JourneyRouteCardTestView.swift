import SwiftUI

/// 旅程路线卡片测试视图
/// 用于展示复刻的首页行程卡片设计
struct JourneyRouteCardTestView: View {
    
    // MARK: - Mock Data
    
    private let mockJourney = DiscoverDetailModels.JourneyRoute(
        id: "route_001",
        title: "日本关西樱花季",
        description: "专为樱花季设计的关西深度游路线",
        duration: 6,
        destinations: [
            DiscoverDetailModels.RouteDestination(
                id: "dest_001",
                name: "东京",
                location: "东京都",
                coordinate: DiscoverDetailModels.Coordinate(latitude: 35.6762, longitude: 139.6503),
                order: 1,
                stayDuration: "2天",
                description: "日本首都",
                imageUrl: "https://images.unsplash.com/photo-1540959733332-eab4deabeeaf",
                attractions: [],
                activities: ["观光", "购物"],
                tips: ["提前预订酒店"]
            ),
            DiscoverDetailModels.RouteDestination(
                id: "dest_002", 
                name: "京都",
                location: "京都府",
                coordinate: DiscoverDetailModels.Coordinate(latitude: 35.0116, longitude: 135.7681),
                order: 2,
                stayDuration: "2天",
                description: "古都京都",
                imageUrl: "https://images.unsplash.com/photo-1493976040374-85c8e12f0c0e",
                attractions: [],
                activities: ["寺庙参观", "传统文化体验"],
                tips: ["穿舒适的鞋子"]
            ),
            DiscoverDetailModels.RouteDestination(
                id: "dest_003",
                name: "大阪",
                location: "大阪府",
                coordinate: DiscoverDetailModels.Coordinate(latitude: 34.6937, longitude: 135.5023),
                order: 3,
                stayDuration: "2天",
                description: "美食之都",
                imageUrl: "https://images.unsplash.com/photo-1590559899731-a382839e5549",
                attractions: [],
                activities: ["美食体验", "购物"],
                tips: ["尝试当地特色小吃"]
            )
        ],
        totalDistance: 500.0,
        estimatedCost: DiscoverDetailModels.CostRange(min: 8000, max: 12000),
        difficulty: .easy,
        forkCount: 256,
        tags: ["樱花", "文化", "美食"],
        highlights: ["樱花盛开", "古都风情", "美食天堂"],
        tips: ["3月下旬至4月上旬为最佳时间"],
        bestSeasons: ["春季"],
        createdAt: Date(),
        updatedAt: Date()
    )
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 标题
                    VStack(alignment: .leading, spacing: 8) {
                        Text("复刻首页行程卡片")
                            .font(.title2)
                            .fontWeight(.bold)

                        Text("复刻首页行程卡片设计，右上角查看按钮，右下角Fork按钮")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal)
                    
                    // 卡片展示
                    VStack(spacing: 24) {
                        JourneyRouteCardView(
                            journey: mockJourney,
                            onViewTap: {
                                print("查看按钮被点击")
                            },
                            onForkTap: {
                                print("复制行程按钮被点击")
                            }
                        )

                        JourneyRouteCardView(
                            journey: mockJourney,
                            onViewTap: {
                                print("查看按钮被点击")
                            },
                            onForkTap: {
                                print("复制行程按钮被点击")
                            }
                        )
                    }
                    .padding(.horizontal)
                    
                    // 说明文字
                    VStack(alignment: .leading, spacing: 12) {
                        Text("设计特点")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            FeatureItem(
                                icon: "text.badge.plus",
                                title: "小标题",
                                description: "添加了\"相关路线\"小标题，包含地图图标"
                            )

                            FeatureItem(
                                icon: "photo",
                                title: "左侧图片",
                                description: "120x80 尺寸，圆角设计，星空渐变背景"
                            )

                            FeatureItem(
                                icon: "text.alignleft",
                                title: "右侧内容",
                                description: "标题、目的地、天数（如6天）、日期、状态信息"
                            )

                            FeatureItem(
                                icon: "hand.tap",
                                title: "操作按钮",
                                description: "右上角查看按钮，右下角复制行程按钮"
                            )

                            FeatureItem(
                                icon: "number",
                                title: "Fork数量",
                                description: "图片右上角显示Fork数量浮层"
                            )

                            FeatureItem(
                                icon: "paintbrush",
                                title: "视觉一致",
                                description: "与首页行程卡片保持完全一致的设计语言"
                            )
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.secondarySystemBackground))
                    )
                    .padding(.horizontal)
                    
                    Spacer(minLength: 50)
                }
                .padding(.vertical)
            }
            .background(Color(.systemGroupedBackground))
            .navigationTitle("卡片测试")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

// MARK: - Supporting Views

private struct FeatureItem: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.blue)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(nil)
            }
            
            Spacer()
        }
    }
}

// MARK: - Preview

#if DEBUG
struct JourneyRouteCardTestView_Previews: PreviewProvider {
    static var previews: some View {
        JourneyRouteCardTestView()
    }
}
#endif
