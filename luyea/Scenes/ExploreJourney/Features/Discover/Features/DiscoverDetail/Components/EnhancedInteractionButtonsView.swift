import SwiftUI

/// 增强版互动按钮视图
/// 包含点赞、收藏、评论、分享、Fork等功能
struct EnhancedInteractionButtonsView: View {
    
    // MARK: - Properties
    
    @Binding var isLiked: Bool
    @Binding var isCollected: Bool
    @Binding var isFollowingAuthor: Bool
    
    let likeCount: Int
    let commentCount: Int
    let shareCount: Int
    let forkCount: Int
    
    let onLike: () -> Void
    let onCollect: () -> Void
    let onComment: () -> Void
    let onShare: () -> Void
    let onFork: () -> Void
    
    // MARK: - Body
    
    var body: some View {
        VStack(spacing: 16) {
            // 主要互动按钮行
            HStack(spacing: 24) {
                // 点赞按钮
                EnhancedInteractionButton(
                    icon: isLiked ? "heart.fill" : "heart",
                    count: likeCount,
                    isActive: isLiked,
                    activeColor: .red,
                    action: onLike
                )

                // 评论按钮
                EnhancedInteractionButton(
                    icon: "message",
                    count: commentCount,
                    isActive: false,
                    activeColor: .blue,
                    action: onComment
                )

                // 收藏按钮
                EnhancedInteractionButton(
                    icon: isCollected ? "bookmark.fill" : "bookmark",
                    count: 0, // 收藏不显示数量
                    isActive: isCollected,
                    activeColor: .orange,
                    action: onCollect
                )

                // 分享按钮
                EnhancedInteractionButton(
                    icon: "square.and.arrow.up",
                    count: shareCount,
                    isActive: false,
                    activeColor: .green,
                    action: onShare
                )
                
                Spacer()
            }
            
            // Fork按钮（如果有旅程路线）
            if forkCount > 0 {
                HStack {
                    Button(action: onFork) {
                        HStack(spacing: 8) {
                            Image(systemName: "arrow.branch")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.blue)
                            
                            Text("Fork到我的行程")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.blue)
                            
                            Spacer()
                            
                            Text("\(forkCount)")
                                .font(.caption)
                                .foregroundColor(.gray)
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(Color.blue.opacity(0.1))
                        .cornerRadius(8)
                    }
                }
            }
        }
        .padding(.horizontal, 16)
    }
}

// MARK: - 互动按钮组件

private struct EnhancedInteractionButton: View {
    let icon: String
    let count: Int
    let isActive: Bool
    let activeColor: Color
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Image(systemName: icon)
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(isActive ? activeColor : .gray)

                if count > 0 {
                    Text("\(count)")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 32) {
        EnhancedInteractionButtonsView(
            isLiked: .constant(false),
            isCollected: .constant(false),
            isFollowingAuthor: .constant(false),
            likeCount: 128,
            commentCount: 45,
            shareCount: 23,
            forkCount: 12,
            onLike: {},
            onCollect: {},
            onComment: {},
            onShare: {},
            onFork: {}
        )
        
        EnhancedInteractionButtonsView(
            isLiked: .constant(true),
            isCollected: .constant(true),
            isFollowingAuthor: .constant(true),
            likeCount: 256,
            commentCount: 89,
            shareCount: 45,
            forkCount: 0, // 无Fork功能
            onLike: {},
            onCollect: {},
            onComment: {},
            onShare: {},
            onFork: {}
        )
    }
    .padding()
}
