import SwiftUI

// MARK: - InteractionButtonsView

/// 交互按钮组件
///
/// **功能概述:**
/// - 提供点赞、评论、分享三个主要交互功能
/// - 支持动态状态显示（点赞状态、计数等）
/// - 集成触觉反馈和动画效果
/// - 响应式布局设计
///
/// **设计特色:**
/// - 点赞按钮支持状态切换和动画
/// - 触觉反馈增强用户体验
/// - 统一的按钮样式和间距
/// - 数字计数显示
struct InteractionButtonsView: View {

    // MARK: - Properties

    /// 点赞状态绑定
    @Binding var isLiked: Bool
    /// 点赞数量
    let likeCount: Int
    /// 评论数量
    let commentCount: Int
    /// 分享数量
    let shareCount: Int
    /// 点赞回调
    let onLike: () -> Void
    /// 评论回调
    let onComment: () -> Void
    /// 分享回调
    let onShare: () -> Void

    // MARK: - Body

    var body: some View {
        HStack(spacing: 24) {
            // 点赞按钮
            likeButton

            // 评论按钮
            commentButton

            // 分享按钮
            shareButton

            Spacer()
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
    }

    // MARK: - Component Views

    /// 点赞按钮
    private var likeButton: some View {
        InteractionButton(
            icon: isLiked ? "heart.fill" : "heart",
            count: likeCount,
            isActive: isLiked,
            activeColor: .red,
            action: handleLikeAction
        )
    }

    /// 评论按钮
    private var commentButton: some View {
        InteractionButton(
            icon: "message",
            count: commentCount,
            isActive: false,
            activeColor: .blue,
            action: onComment
        )
    }

    /// 分享按钮
    private var shareButton: some View {
        InteractionButton(
            icon: "square.and.arrow.up",
            count: shareCount,
            isActive: false,
            activeColor: .green,
            action: onShare
        )
    }

    // MARK: - Helper Methods

    /// 处理点赞操作
    private func handleLikeAction() {
        // 添加触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()

        // 添加弹簧动画
        withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
            isLiked.toggle()
        }

        // 执行回调
        onLike()
    }
}

// MARK: - InteractionButton

/// 单个交互按钮组件
///
/// **功能概述:**
/// - 可配置的图标和计数显示
/// - 支持激活状态和颜色变化
/// - 集成按压动画效果
/// - 智能数字格式化（1k+）
struct InteractionButton: View {

    // MARK: - Properties

    /// 图标名称
    let icon: String
    /// 计数数值
    let count: Int
    /// 是否激活状态
    let isActive: Bool
    /// 激活时的颜色
    let activeColor: Color
    /// 点击回调
    let action: () -> Void

    /// 按压状态
    @State private var isPressed = false

    // MARK: - Body

    var body: some View {
        Button(action: action) {
            buttonContent
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }

    /// 按钮内容
    private var buttonContent: some View {
        HStack(spacing: 6) {
            // 图标
            buttonIcon

            // 计数文本（如果大于0）
            if count > 0 {
                countText
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(buttonBackground)
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
    }

    /// 按钮图标
    private var buttonIcon: some View {
        Image(systemName: icon)
            .font(.system(size: 18, weight: .medium))
            .foregroundColor(isActive ? activeColor : .secondary)
            .scaleEffect(isActive ? 1.1 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isActive)
    }

    /// 计数文本
    private var countText: some View {
        Text(formatCount(count))
            .font(.caption)
            .fontWeight(.medium)
            .foregroundColor(.secondary)
    }

    /// 按钮背景
    private var buttonBackground: some View {
        RoundedRectangle(cornerRadius: 20)
            .fill(isActive ? activeColor.opacity(0.1) : Color.clear)
            .animation(.easeInOut(duration: 0.2), value: isActive)
    }

    // MARK: - Helper Methods

    /// 格式化数字显示
    /// - Parameter count: 原始数字
    /// - Returns: 格式化后的字符串（如：1.2k）
    private func formatCount(_ count: Int) -> String {
        if count >= 1000 {
            return String(format: "%.1fk", Double(count) / 1000.0)
        }
        return "\(count)"
    }
}

// MARK: - Preview

#Preview("未点赞状态") {
    InteractionButtonsView(
        isLiked: .constant(false),
        likeCount: 128,
        commentCount: 45,
        shareCount: 23,
        onLike: { print("点赞") },
        onComment: { print("评论") },
        onShare: { print("分享") }
    )
    .padding()
    .background(Color(.systemBackground))
}

#Preview("已点赞状态") {
    InteractionButtonsView(
        isLiked: .constant(true),
        likeCount: 129,
        commentCount: 45,
        shareCount: 23,
        onLike: { print("点赞") },
        onComment: { print("评论") },
        onShare: { print("分享") }
    )
    .padding()
    .background(Color(.systemBackground))
}

#Preview("大数值测试") {
    InteractionButtonsView(
        isLiked: .constant(false),
        likeCount: 1234,
        commentCount: 567,
        shareCount: 89,
        onLike: { print("点赞") },
        onComment: { print("评论") },
        onShare: { print("分享") }
    )
    .padding()
    .background(Color(.systemBackground))
}
