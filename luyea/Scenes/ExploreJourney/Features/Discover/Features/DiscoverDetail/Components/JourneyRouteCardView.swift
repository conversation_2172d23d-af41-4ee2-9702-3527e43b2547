import SwiftUI

/// 旅程路线卡片视图
/// 复刻首页行程卡片设计，简洁版本
struct JourneyRouteCardView: View {

    // MARK: - Properties

    let journey: DiscoverDetailModels.JourneyRoute
    let onViewTap: () -> Void
    let onForkTap: () -> Void

    // MARK: - Constants

    private enum ImageConstants {
        static let width: CGFloat = 120
        static let minHeight: CGFloat = 80
        static let cornerRadius: CGFloat = 8
    }



    // MARK: - Body

    var body: some View {
        VStack(spacing: 12) {
            // 相关路线小标题
            relatedRouteSectionHeader

            // 路线卡片
            VStack(spacing: 0) {
                HStack(alignment: .top, spacing: 16) {
                    // 左侧：路线封面图片（自适应高度）
                    routeCoverImageView

                    // 右侧：路线信息
                    ZStack(alignment: .topTrailing) {
                        VStack(alignment: .leading, spacing: 5) {
                            // 路线标题（为右上角按钮留出空间）
                            HStack {
                                Text(journey.title)
                                    .font(.system(size: 15, weight: .semibold))
                                    .foregroundColor(.primary)
                                    .lineLimit(2)
                                    .multilineTextAlignment(.leading)

                                Spacer(minLength: 50) // 为右上角按钮预留空间
                            }

                            // 目的地信息
                            destinationInfoView

                            // 天数和日期信息
                            durationAndDateView

                            Spacer(minLength: 0)

                            // 状态标签和右下角按钮
                            HStack(alignment: .bottom) {
                                statusBadgeView

                                Spacer()

                                // 右下角复制行程按钮
                                forkButton
                            }
                        }
                        .frame(maxWidth: .infinity, alignment: .leading)

                        // 右上角查看按钮（绝对定位）
                        viewButton
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
                .padding(16)
            }
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: Color.black.opacity(0.08), radius: 8, x: 0, y: 2)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.gray.opacity(0.1), lineWidth: 1)
            )
        }
    }

    // MARK: - Private Views

    /// 相关路线小标题
    private var relatedRouteSectionHeader: some View {
        HStack {
            HStack(spacing: 6) {
                Image(systemName: "map")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.blue)

                Text("相关路线")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.primary)
            }

            Spacer()
        }
        .padding(.horizontal, 16)
    }

    /// 路线封面图片视图
    private var routeCoverImageView: some View {
        ZStack {
            AsyncImage(url: URL(string: getRouteImageUrl())) { image in
                image
                    .resizable()
                    .scaledToFill()
            } placeholder: {
                // 星空背景占位图
                LinearGradient(
                    colors: [
                        Color.blue.opacity(0.8),
                        Color.purple.opacity(0.6),
                        Color.black.opacity(0.9)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            }
            .frame(width: ImageConstants.width)
            .frame(minHeight: ImageConstants.minHeight)
            .frame(maxHeight: .infinity)
            .clipShape(RoundedRectangle(cornerRadius: ImageConstants.cornerRadius))

            // Fork数量浮层 - 固定在图片右上角
            if let forkCount = journey.forkCount, forkCount > 0 {
                VStack {
                    HStack {
                        Spacer()
                        forkCountOverlay
                    }
                    Spacer()
                }
                .frame(width: ImageConstants.width)
                .frame(maxHeight: .infinity)
                .padding(6)
            }
        }
    }

    /// 目的地信息视图
    private var destinationInfoView: some View {
        HStack(spacing: 6) {
            Image(systemName: "location.fill")
                .font(.system(size: 11, weight: .medium))
                .foregroundColor(.blue)

            Text(getDestinationText())
                .font(.system(size: 13, weight: .medium))
                .foregroundColor(.blue)

            Spacer()
        }
    }

    /// 天数和日期信息视图
    private var durationAndDateView: some View {
        HStack(spacing: 16) {
            // 天数（显示完整格式，如"6天"）
            Text("\(journey.duration)天")
                .font(.system(size: 13, weight: .semibold))
                .foregroundColor(.orange)

            // 日期占位
            Text("占位日期")
                .font(.system(size: 13, weight: .regular))
                .foregroundColor(.secondary)

            Spacer()
        }
    }

    /// 状态标签视图
    private var statusBadgeView: some View {
        HStack {
            HStack(spacing: 4) {
                Circle()
                    .fill(Color.gray)
                    .frame(width: 6, height: 6)

                Text("暂时占位")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
    }

    /// 右上角查看按钮（与行程列表样式一致，带点击效果）
    private var viewButton: some View {
        Button(action: onViewTap) {
            VStack(spacing: 2) {
                Image(systemName: "eye.circle.fill")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.blue)

                Text("查看")
                    .font(.system(size: 9, weight: .medium))
                    .foregroundColor(.blue)
            }
        }
        .buttonStyle(ViewButtonStyle())
        .accessibilityLabel("查看路线")
    }

    /// Fork数量浮层
    private var forkCountOverlay: some View {
        HStack(spacing: 3) {
            Image(systemName: "arrow.branch")
                .font(.system(size: 10, weight: .medium))

            Text("\(journey.forkCount ?? 0)")
                .font(.system(size: 10, weight: .semibold))
        }
        .foregroundColor(.white)
        .padding(.horizontal, 6)
        .padding(.vertical, 3)
        .background(Color.black.opacity(0.6))
        .clipShape(Capsule())
    }

    /// 右下角复制行程按钮
    private var forkButton: some View {
        Button(action: onForkTap) {
            HStack(spacing: 3) {
                Image(systemName: "doc.on.doc")
                    .font(.system(size: 10, weight: .medium))

                Text("复制行程")
                    .font(.system(size: 10, weight: .medium))
            }
            .foregroundColor(.blue)
            .padding(.horizontal, 7)
            .padding(.vertical, 3)
            .background(Color.blue.opacity(0.08))
            .clipShape(Capsule())
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Private Methods

    /// 获取路线图片URL
    private func getRouteImageUrl() -> String {
        // 使用第一个目的地的图片，或者返回默认图片
        return journey.destinations.first?.imageUrl ?? ""
    }

    /// 获取目的地文本
    private func getDestinationText() -> String {
        let destinationNames = journey.destinations.prefix(3).map { $0.name }
        if journey.destinations.count > 3 {
            return destinationNames.joined(separator: " / ") + " 等"
        } else {
            return destinationNames.joined(separator: " / ")
        }
    }
}

// MARK: - Custom Button Styles

/// 查看按钮的自定义样式，提供点击效果
struct ViewButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.85 : 1.0)
            .opacity(configuration.isPressed ? 0.7 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Preview

#if DEBUG
struct JourneyRouteCardView_Previews: PreviewProvider {
    static var previews: some View {
        let mockJourney = DiscoverDetailModels.JourneyRoute(
            id: "route_001",
            title: "日本关西樱花季",
            description: "专为樱花季设计的关西深度游路线",
            duration: 6,
            destinations: [
                DiscoverDetailModels.RouteDestination(
                    id: "dest_001",
                    name: "东京",
                    location: "东京都",
                    coordinate: DiscoverDetailModels.Coordinate(latitude: 35.6762, longitude: 139.6503),
                    order: 1,
                    stayDuration: "2天",
                    description: "日本首都",
                    imageUrl: "https://images.unsplash.com/photo-1540959733332-eab4deabeeaf",
                    attractions: [],
                    activities: ["观光", "购物"],
                    tips: ["提前预订酒店"]
                ),
                DiscoverDetailModels.RouteDestination(
                    id: "dest_002",
                    name: "京都",
                    location: "京都府",
                    coordinate: DiscoverDetailModels.Coordinate(latitude: 35.0116, longitude: 135.7681),
                    order: 2,
                    stayDuration: "2天",
                    description: "古都京都",
                    imageUrl: "https://images.unsplash.com/photo-1493976040374-85c8e12f0c0e",
                    attractions: [],
                    activities: ["寺庙参观", "传统文化体验"],
                    tips: ["穿舒适的鞋子"]
                ),
                DiscoverDetailModels.RouteDestination(
                    id: "dest_003",
                    name: "大阪",
                    location: "大阪府",
                    coordinate: DiscoverDetailModels.Coordinate(latitude: 34.6937, longitude: 135.5023),
                    order: 3,
                    stayDuration: "2天",
                    description: "美食之都",
                    imageUrl: "https://images.unsplash.com/photo-1590559899731-a382839e5549",
                    attractions: [],
                    activities: ["美食体验", "购物"],
                    tips: ["尝试当地特色小吃"]
                )
            ],
            totalDistance: 500.0,
            estimatedCost: DiscoverDetailModels.CostRange(min: 8000, max: 12000),
            difficulty: .easy,
            forkCount: 256,
            tags: ["樱花", "文化", "美食"],
            highlights: ["樱花盛开", "古都风情", "美食天堂"],
            tips: ["3月下旬至4月上旬为最佳时间"],
            bestSeasons: ["春季"],
            createdAt: Date(),
            updatedAt: Date()
        )

        VStack(spacing: 20) {
            JourneyRouteCardView(
                journey: mockJourney,
                onViewTap: {
                    print("查看按钮被点击")
                },
                onForkTap: {
                    print("复制行程按钮被点击")
                }
            )
        }
        .padding()
        .background(Color(.systemGroupedBackground))
        .previewDisplayName("旅程路线卡片 - 复刻版")
    }
}
#endif
