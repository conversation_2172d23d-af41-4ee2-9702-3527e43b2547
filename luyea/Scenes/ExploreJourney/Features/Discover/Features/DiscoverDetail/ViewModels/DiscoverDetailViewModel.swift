import SwiftUI
import Combine

/// 发现详情页视图模型
///
/// 负责管理详情页的业务逻辑，包括点赞、评论、分享、Fork等功能。
/// 遵循MVVM架构，将业务逻辑从View中分离。
/// 使用独立的数据模型和服务，通过导航参数获取详情数据。
@MainActor
class DiscoverDetailViewModel: ObservableObject {

    // MARK: - Published Properties

    /// 详情内容数据
    @Published var detailContent: DiscoverDetailModels.DetailContent?

    /// 评论列表
    @Published var comments: [DiscoverDetailModels.Comment] = []

    /// 当前页码（图片轮播）
    @Published var currentPage: Int = 0

    /// 评论输入文本
    @Published var commentText: String = ""

    /// 是否显示评论输入框
    @Published var showCommentInput: Bool = false

    /// 是否显示作者个人资料
    @Published var isAuthorProfilePresented: Bool = false

    /// 是否自动滚动
    @Published var isAutoScrolling: Bool = true

    /// 是否显示全屏图片查看器
    @Published var showFullScreenViewer: Bool = false

    /// 加载状态
    @Published var isLoading: Bool = false

    /// 评论加载状态
    @Published var isLoadingComments: Bool = false

    /// 错误信息
    @Published var error: String?

    /// 是否显示旅程路线详情
    @Published var showJourneyDetail: Bool = false

    // MARK: - Computed Properties

    /// 预览数据（从导航参数获取）
    var previewData: DiscoverDetailModels.PreviewData? {
        navigationParams.previewData
    }

    // MARK: - Private Properties

    /// 导航参数
    private let navigationParams: DiscoverDetailModels.NavigationParams

    /// 详情页服务
    private let detailService: DiscoverDetailServiceProtocol

    /// 取消订阅集合
    private var cancellables = Set<AnyCancellable>()

    /// 评论分页信息
    private var currentCommentsPage = 1
    private var hasMoreComments = true

    /// 重试计数器
    private var retryCount = 0
    private let maxRetryCount = 3

    /// 加载状态枚举
    enum LoadingState {
        case idle
        case loading
        case loaded
        case failed(Error)
        case retrying
    }

    /// 当前加载状态
    @Published private(set) var loadingState: LoadingState = .idle
    
    // MARK: - Computed Properties

    /// 是否有多张图片
    var hasMultipleImages: Bool {
        guard let content = detailContent else { return false }
        return content.images.count > 1
    }

    /// 当前点赞数
    var likeCount: Int {
        detailContent?.stats.likeCount ?? 0
    }

    /// 评论数量
    var commentCount: Int {
        detailContent?.stats.commentCount ?? 0
    }

    /// Fork数量
    var forkCount: Int {
        detailContent?.stats.forkCount ?? 0
    }

    /// 分享数量
    var shareCount: Int {
        detailContent?.stats.shareCount ?? 0
    }

    /// 是否已点赞
    var isLiked: Bool {
        get {
            detailContent?.stats.userInteraction.isLiked ?? false
        }
        set {
            // 更新本地状态，实际的网络请求在handleLike中处理
            if var content = detailContent {
                content.stats.userInteraction.isLiked = newValue
                detailContent = content
            }
        }
    }

    /// 是否已收藏
    var isCollected: Bool {
        get {
            detailContent?.stats.userInteraction.isCollected ?? false
        }
        set {
            if var content = detailContent {
                content.stats.userInteraction.isCollected = newValue
                detailContent = content
            }
        }
    }

    /// 是否已关注作者
    var isFollowingAuthor: Bool {
        get {
            detailContent?.stats.userInteraction.isFollowingAuthor ?? false
        }
        set {
            if var content = detailContent {
                content.stats.userInteraction.isFollowingAuthor = newValue
                detailContent = content
            }
        }
    }

    /// 是否有旅程路线
    var hasJourney: Bool {
        detailContent?.hasJourney ?? false
    }

    // MARK: - Initialization

    /// 初始化视图模型
    /// - Parameters:
    ///   - navigationParams: 导航参数
    ///   - detailService: 详情页服务实例
    public init(
        navigationParams: DiscoverDetailModels.NavigationParams,
        detailService: DiscoverDetailServiceProtocol = DiscoverDetailService()
    ) {
        self.navigationParams = navigationParams
        self.detailService = detailService

        setupBindings()

        // 自动加载详情数据
        Task {
            await loadDetailContent()
        }
    }
    
    // MARK: - Public Methods

    /// 加载详情内容
    func loadDetailContent() async {
        await loadDetailContentWithRetry()
    }

    /// 带重试机制的加载详情内容
    private func loadDetailContentWithRetry() async {
        guard !isLoading else { return }

        await MainActor.run {
            self.isLoading = true
            self.error = nil
            self.loadingState = retryCount > 0 ? .retrying : .loading
        }

        do {
            let content = try await detailService.fetchDetailContent(
                id: navigationParams.id
            )

            await MainActor.run {
                self.detailContent = content
                self.isLoading = false
                self.loadingState = .loaded
                self.retryCount = 0 // 重置重试计数

                // 自动加载评论
                Task {
                    await self.loadComments()
                }
            }

        } catch {
            await MainActor.run {
                self.isLoading = false
                self.loadingState = .failed(error)

                if self.retryCount < self.maxRetryCount {
                    self.retryCount += 1
                    Log.warning("⚠️ [DiscoverDetailViewModel] 详情内容加载失败，准备重试 (\(self.retryCount)/\(self.maxRetryCount)): \(error)")

                    // 延迟重试
                    Task {
                        try? await Task.sleep(nanoseconds: UInt64(self.retryCount) * 1_000_000_000) // 递增延迟
                        await self.loadDetailContentWithRetry()
                    }
                } else {
                    self.error = self.getErrorMessage(from: error)
                    Log.error("❌ [DiscoverDetailViewModel] 详情内容加载失败，已达最大重试次数: \(error)")
                }
            }
        }
    }

    /// 获取用户友好的错误信息
    private func getErrorMessage(from error: Error) -> String {
        if let urlError = error as? URLError {
            switch urlError.code {
            case .notConnectedToInternet:
                return "网络连接不可用，请检查网络设置"
            case .timedOut:
                return "请求超时，请稍后重试"
            case .cannotFindHost, .cannotConnectToHost:
                return "无法连接到服务器，请稍后重试"
            default:
                return "网络错误，请稍后重试"
            }
        }
        return error.localizedDescription
    }

    /// 加载评论列表
    func loadComments(refresh: Bool = false) async {
        guard !isLoadingComments else { return }

        if refresh {
            currentCommentsPage = 1
            hasMoreComments = true
        }

        guard hasMoreComments else { return }

        isLoadingComments = true

        do {
            let response = try await detailService.fetchComments(
                contentId: navigationParams.id,
                page: currentCommentsPage,
                pageSize: 20
            )

            await MainActor.run {
                if refresh {
                    self.comments = response.items
                } else {
                    self.comments.append(contentsOf: response.items)
                }

                self.currentCommentsPage += 1
                self.hasMoreComments = response.hasMore
                self.isLoadingComments = false
            }

        } catch {
            await MainActor.run {
                self.isLoadingComments = false
                Log.error("❌ [DiscoverDetailViewModel] 评论加载失败: \(error)")
            }
        }
    }

    /// 处理点赞操作
    func handleLike() {
        Task {
            await toggleLike()
        }
    }

    /// 处理收藏操作
    func handleCollect() {
        Task {
            await toggleCollect()
        }
    }

    /// 处理关注作者操作
    func handleFollowAuthor() {
        Task {
            await toggleFollowAuthor()
        }
    }

    /// 处理Fork旅程操作
    func handleForkJourney() {
        guard let journey = detailContent?.journey else { return }

        Task {
            await forkJourney(journeyId: journey.id)
        }
    }

    /// 处理评论提交
    func handleCommentSubmission() {
        guard !commentText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return
        }

        Task {
            await submitComment()
        }
    }

    /// 处理分享操作
    func handleShare() {
        Task {
            await shareContent()
        }
    }

    /// 显示作者个人资料
    func showAuthorProfile() {
        isAuthorProfilePresented = true
    }

    /// 显示旅程路线详情
    func showJourneyRouteDetail() {
        showJourneyDetail = true
    }

    /// 显示全屏图片查看器
    func showFullScreenImageViewer() {
        withAnimation(.spring(response: 0.55, dampingFraction: 0.85, blendDuration: 0.05)) {
            showFullScreenViewer = true
        }
    }

    /// 处理缩放状态变化
    func handleZoomChanged(_ isZoomed: Bool) {
        if !showFullScreenViewer {
            isAutoScrolling = !isZoomed
        }
    }
    
    // MARK: - Private Methods
    
    /// 设置数据绑定
    private func setupBindings() {
        // 监听全屏查看器状态变化
        $showFullScreenViewer
            .sink { [weak self] isShowing in
                guard let self = self else { return }
                if isShowing {
                    // 显示全屏查看器时，停止自动滚动
                    self.isAutoScrolling = false
                } else if self.hasMultipleImages {
                    // 关闭全屏查看器时，如果有多张图片则恢复自动滚动
                    self.isAutoScrolling = true
                }
            }
            .store(in: &cancellables)
    }
    
    /// 切换点赞状态
    private func toggleLike() async {
        guard let content = detailContent else { return }

        let currentLikedState = content.stats.userInteraction.isLiked

        do {
            let newLikedState = try await detailService.toggleLike(
                contentId: navigationParams.id,
                isLiked: currentLikedState
            )

            await MainActor.run {
                // 更新本地状态
                self.detailContent?.stats.userInteraction.isLiked = newLikedState
                if newLikedState {
                    self.detailContent?.stats.likeCount += 1
                } else {
                    self.detailContent?.stats.likeCount = max(0, (self.detailContent?.stats.likeCount ?? 1) - 1)
                }

                ToastManager.shared.show(
                    newLikedState ? "已点赞" : "已取消点赞",
                    style: .success
                )
            }
        } catch {
            await MainActor.run {
                ToastManager.shared.show("操作失败，请重试", style: .error)
                Log.error("❌ [DiscoverDetailViewModel] 点赞操作失败: \(error)")
            }
        }
    }
    
    /// 切换收藏状态
    private func toggleCollect() async {
        guard let content = detailContent else { return }

        let currentCollectedState = content.stats.userInteraction.isCollected

        do {
            let newCollectedState = try await detailService.toggleCollect(
                contentId: navigationParams.id,
                isCollected: currentCollectedState
            )

            await MainActor.run {
                // 更新本地状态
                self.detailContent?.stats.userInteraction.isCollected = newCollectedState
                if newCollectedState {
                    self.detailContent?.stats.collectCount += 1
                } else {
                    self.detailContent?.stats.collectCount = max(0, (self.detailContent?.stats.collectCount ?? 1) - 1)
                }

                ToastManager.shared.show(
                    newCollectedState ? "已收藏" : "已取消收藏",
                    style: .success
                )
            }
        } catch {
            await MainActor.run {
                ToastManager.shared.show("操作失败，请重试", style: .error)
                Log.error("❌ [DiscoverDetailViewModel] 收藏操作失败: \(error)")
            }
        }
    }

    /// 切换关注作者状态
    private func toggleFollowAuthor() async {
        guard let content = detailContent else { return }

        let currentFollowingState = content.stats.userInteraction.isFollowingAuthor

        do {
            let newFollowingState = try await detailService.toggleFollowAuthor(
                authorId: content.author.id,
                isFollowing: currentFollowingState
            )

            await MainActor.run {
                // 更新本地状态
                self.detailContent?.stats.userInteraction.isFollowingAuthor = newFollowingState

                ToastManager.shared.show(
                    newFollowingState ? "已关注" : "已取消关注",
                    style: .success
                )
            }
        } catch {
            await MainActor.run {
                ToastManager.shared.show("操作失败，请重试", style: .error)
                Log.error("❌ [DiscoverDetailViewModel] 关注操作失败: \(error)")
            }
        }
    }

    /// Fork旅程路线
    private func forkJourney(journeyId: String) async {
        do {
            let newJourneyId = try await detailService.forkJourney(journeyId: journeyId)

            await MainActor.run {
                // 更新Fork数量
                self.detailContent?.stats.forkCount += 1

                ToastManager.shared.show("旅程已Fork到您的行程中", style: .success)

                // 可以在这里导航到新的行程页面
                // NavigationManager.shared.navigateToItinerary(id: newJourneyId)
            }
        } catch {
            await MainActor.run {
                ToastManager.shared.show("Fork失败，请重试", style: .error)
                Log.error("❌ [DiscoverDetailViewModel] Fork操作失败: \(error)")
            }
        }
    }

    /// 提交评论
    private func submitComment() async {
        let content = commentText.trimmingCharacters(in: .whitespacesAndNewlines)

        // 清空输入框
        await MainActor.run {
            self.commentText = ""
            self.showCommentInput = false
        }

        do {
            let newComment = try await detailService.submitComment(
                contentId: navigationParams.id,
                content: content,
                parentCommentId: nil
            )

            await MainActor.run {
                // 将新评论添加到列表顶部
                self.comments.insert(newComment, at: 0)

                // 更新评论数量
                self.detailContent?.stats.commentCount += 1

                ToastManager.shared.show("评论发布成功", style: .success)
            }
        } catch {
            await MainActor.run {
                ToastManager.shared.show("评论发布失败，请重试", style: .error)
                // 恢复输入内容
                self.commentText = content
                Log.error("❌ [DiscoverDetailViewModel] 评论提交失败: \(error)")
            }
        }
    }

    /// 分享内容
    private func shareContent() async {
        do {
            let shareUrl = try await detailService.shareContent(contentId: navigationParams.id, platform: nil)

            await MainActor.run {
                // 更新分享数量
                self.detailContent?.stats.shareCount += 1

                // 调用系统分享
                self.presentSystemShare(url: shareUrl)
            }
        } catch {
            await MainActor.run {
                ToastManager.shared.show("分享链接生成失败", style: .error)
                Log.error("❌ [DiscoverDetailViewModel] 分享操作失败: \(error)")
            }
        }
    }

    /// 调用系统分享
    private func presentSystemShare(url: String) {
        // 这里可以调用系统分享功能
        // 或者复制到剪贴板
        UIPasteboard.general.string = url
        Task { @MainActor in
            ToastManager.shared.show("分享链接已复制到剪贴板", style: .success)
        }
    }

    /// 处理Fork旅程
    func handleFork() {
        guard let journey = detailContent?.journey else { return }

        Task {
            do {
                // 这里可以调用Fork API
                // let forkResult = try await detailService.forkJourney(journeyId: journey.id)

                await MainActor.run {
                    // 更新Fork数量
                    if var content = detailContent {
                        content.stats.forkCount += 1
                        detailContent = content
                    }

                    // 显示成功提示
                    Task { @MainActor in
                        ToastManager.shared.show("旅程已Fork到您的行程", style: .success)
                    }
                }
            } catch {
                await MainActor.run {
                    self.error = "Fork失败: \(error.localizedDescription)"
                }
            }
        }
    }

    // MARK: - Preview Support

    /// 创建预览用的视图模型
    @MainActor
    static func preview() -> DiscoverDetailViewModel {
        let mockParams = DiscoverDetailModels.NavigationParams(
            id: "preview_001",
            previewData: DiscoverDetailModels.PreviewData(
                title: "预览标题",
                coverImage: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4",
                authorName: "预览用户",
                authorAvatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e"
            )
        )
        return DiscoverDetailViewModel(navigationParams: mockParams)
    }
}
