import SwiftUI

/// 旅程详情页视图
/// 展示完整的旅程路线信息，支持交互和动画效果
struct JourneyDetailView: View {

    // MARK: - Properties

    let journey: DiscoverDetailModels.JourneyRoute
    @Environment(\.dismiss) private var dismiss

    // MARK: - State Properties

    /// 选中的目的地索引
    @State private var selectedDestinationIndex: Int?
    /// 是否显示Fork确认
    @State private var showForkConfirmation = false
    /// 滚动位置
    @State private var scrollPosition: Int?
    /// 动画状态
    @State private var animateContent = false
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            ScrollViewReader { proxy in
                ScrollView {
                    VStack(alignment: .leading, spacing: 20) {
                        // 头部信息
                        headerSection
                            .opacity(animateContent ? 1 : 0)
                            .offset(y: animateContent ? 0 : 20)
                            .animation(.easeOut(duration: 0.6), value: animateContent)

                        // 路线概览
                        overviewSection
                            .opacity(animateContent ? 1 : 0)
                            .offset(y: animateContent ? 0 : 20)
                            .animation(.easeOut(duration: 0.6).delay(0.1), value: animateContent)

                        // 目的地列表
                        destinationsSection
                            .opacity(animateContent ? 1 : 0)
                            .offset(y: animateContent ? 0 : 20)
                            .animation(.easeOut(duration: 0.6).delay(0.2), value: animateContent)

                        // 贴士和建议
                        tipsSection
                            .opacity(animateContent ? 1 : 0)
                            .offset(y: animateContent ? 0 : 20)
                            .animation(.easeOut(duration: 0.6).delay(0.3), value: animateContent)

                        // 操作按钮区域
                        actionSection
                            .opacity(animateContent ? 1 : 0)
                            .offset(y: animateContent ? 0 : 20)
                            .animation(.easeOut(duration: 0.6).delay(0.4), value: animateContent)

                        // 底部间距
                        Spacer(minLength: 100)
                    }
                    .padding(.horizontal, 16)
                }
                .onChange(of: scrollPosition) { _, newValue in
                    if let position = newValue {
                        withAnimation(.easeInOut(duration: 0.5)) {
                            proxy.scrollTo(position, anchor: .center)
                        }
                    }
                }
            }
            .navigationTitle("旅程详情")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                    .buttonStyle(.scale)
                }
            }
            .onAppear {
                withAnimation {
                    animateContent = true
                }
            }
        }
        .alert("Fork 这个旅程", isPresented: $showForkConfirmation) {
            Button("取消", role: .cancel) { }
            Button("确认 Fork") {
                forkJourney()
            }
        } message: {
            Text("Fork 后您可以基于这个旅程创建自己的版本")
        }
    }
    
    // MARK: - 头部信息
    
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(journey.title)
                .font(.title2)
                .fontWeight(.bold)
            
            if let description = journey.description {
                Text(description)
                    .font(.body)
                    .foregroundColor(.secondary)
            }
            
            // 标签
            if !journey.tags.isEmpty {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 8) {
                        ForEach(journey.tags, id: \.self) { tag in
                            Text(tag)
                                .font(.caption)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.blue.opacity(0.1))
                                .foregroundColor(.blue)
                                .cornerRadius(4)
                        }
                    }
                    .padding(.horizontal, 16)
                }
                .padding(.horizontal, -16)
            }
        }
    }
    
    // MARK: - 路线概览
    
    private var overviewSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("路线概览")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                OverviewCard(
                    icon: "clock",
                    title: "行程时长",
                    value: "\(journey.duration)天"
                )
                
                OverviewCard(
                    icon: "location",
                    title: "目的地",
                    value: "\(journey.destinations.count)个"
                )
                
                OverviewCard(
                    icon: "dollarsign.circle",
                    title: "预估费用",
                    value: journey.estimatedCost.map { "¥\($0.min)-\($0.max)" } ?? "待定"
                )
                
                OverviewCard(
                    icon: "speedometer",
                    title: "难度等级",
                    value: difficultyText
                )
            }
        }
    }
    
    // MARK: - 目的地列表
    
    private var destinationsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("详细路线")
                .font(.headline)
                .fontWeight(.semibold)
            
            ForEach(journey.destinations.sorted(by: { $0.order < $1.order }), id: \.id) { destination in
                DestinationCard(destination: destination)
            }
        }
    }
    
    // MARK: - 贴士和建议
    
    private var tipsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("旅行贴士")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(alignment: .leading, spacing: 8) {
                ForEach(journey.tips, id: \.self) { tip in
                    HStack(alignment: .top, spacing: 8) {
                        Image(systemName: "lightbulb")
                            .font(.caption)
                            .foregroundColor(.orange)
                            .padding(.top, 2)
                        
                        Text(tip)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(12)
            .background(Color(.systemGray6))
            .cornerRadius(8)
        }
    }
    
    // MARK: - 计算属性
    
    private var difficultyText: String {
        return journey.difficulty.displayName
    }
}

// MARK: - 辅助组件

private struct OverviewCard: View {
    let icon: String
    let title: String
    let value: String
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.blue)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
        }
        .frame(maxWidth: .infinity)
        .padding(12)
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

private struct DestinationCard: View {
    let destination: DiscoverDetailModels.RouteDestination
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                // 序号
                Text("\(destination.order)")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .frame(width: 20, height: 20)
                    .background(Color.blue)
                    .clipShape(Circle())
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(destination.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text(destination.location)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                if let stayDuration = destination.stayDuration {
                    Text(stayDuration)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.green.opacity(0.1))
                        .foregroundColor(.green)
                        .cornerRadius(4)
                }
            }
            
            if let description = destination.description {
                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
        .padding(12)
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }

    // MARK: - 操作区域

    private var actionSection: some View {
        VStack(spacing: 16) {
            // Fork按钮
            Button(action: {
                showForkConfirmation = true
            }) {
                HStack {
                    Image(systemName: "arrow.branch")
                        .font(.headline)
                    Text("Fork 这个旅程")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [Color.blue, Color.blue.opacity(0.8)]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                )
            }
            .buttonStyle(.scale)

            // 分享按钮
            Button(action: {
                shareJourney()
            }) {
                HStack {
                    Image(systemName: "square.and.arrow.up")
                        .font(.subheadline)
                    Text("分享旅程")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                .foregroundColor(.blue)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.blue, lineWidth: 1)
                )
            }
            .buttonStyle(.scale)
        }
        .padding(.top, 8)
    }

    // MARK: - Helper Methods

    /// Fork旅程
    private func forkJourney() {
        // TODO: 实现Fork功能
        print("Fork journey: \(journey.title)")
        // 这里可以添加实际的Fork逻辑
        // 例如：导航到创建旅程页面，预填充当前旅程数据
    }

    /// 分享旅程
    private func shareJourney() {
        // TODO: 实现分享功能
        print("Share journey: \(journey.title)")
        // 这里可以添加实际的分享逻辑
        // 例如：调用系统分享面板
    }
}

// MARK: - Preview

#Preview {
    JourneyDetailView(
        journey: DiscoverDetailModels.JourneyRoute(
            id: "journey_001",
            title: "无锡樱花季2日游",
            description: "专为樱花季设计的无锡深度游路线，涵盖鼋头渚、蠡园等赏樱胜地",
            duration: 2,
            destinations: [
                DiscoverDetailModels.RouteDestination(
                    id: "dest_001",
                    name: "鼋头渚风景区",
                    location: "无锡市滨湖区",
                    coordinate: DiscoverDetailModels.Coordinate(latitude: 31.5358, longitude: 120.2297),
                    order: 1,
                    stayDuration: "4小时",
                    description: "太湖第一名胜，樱花盛开时美不胜收",
                    imageUrl: "https://example.com/image.jpg",
                    attractions: [],
                    activities: ["赏樱", "摄影"],
                    tips: ["早上人少景美"]
                )
            ],
            totalDistance: 25.5,
            estimatedCost: DiscoverDetailModels.CostRange(min: 300, max: 500),
            difficulty: .easy,
            forkCount: 127,
            tags: ["樱花", "春游", "摄影", "江南"],
            highlights: ["太湖第一名胜", "最美樱花季"],
            tips: ["3月中旬至4月上旬为最佳时间", "建议住宿太湖边酒店"],
            bestSeasons: ["春季"],
            createdAt: Date(),
            updatedAt: Date()
        )
    )
}
