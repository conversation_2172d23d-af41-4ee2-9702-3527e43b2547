import SwiftUI

// MARK: - DiscoverDetailContentView

/// 发现详情页内容视图
///
/// **功能概述:**
/// - 展示发现内容的详细信息，包括图片轮播、内容信息、旅程路线、互动按钮和评论区
/// - 支持图片全屏查看、点赞、分享、评论等交互功能
/// - 遵循MVVM架构，通过ViewModel处理业务逻辑
/// - 使用独立的数据模型，通过导航参数获取详情数据
///
/// **技术特性:**
/// - 支持iOS 18+ 现代化设计语言
/// - 使用SwiftUI声明式UI构建
/// - 集成触觉反馈和动画效果
/// - 响应式布局适配不同屏幕尺寸
struct DiscoverDetailContentView: View {

    // MARK: - Properties

    /// 导航参数，包含页面初始化所需的数据
    let navigationParams: DiscoverDetailModels.NavigationParams

    /// 环境变量：页面关闭操作
    @Environment(\.dismiss) private var dismiss

    /// 视图模型，负责业务逻辑处理和状态管理
    @StateObject private var viewModel: DiscoverDetailViewModel

    // MARK: - Initialization

    /// 初始化详情页视图
    /// - Parameter navigationParams: 导航参数，包含内容ID和预览数据
    init(navigationParams: DiscoverDetailModels.NavigationParams) {
        self.navigationParams = navigationParams
        self._viewModel = StateObject(wrappedValue: DiscoverDetailViewModel(navigationParams: navigationParams))
    }

    // MARK: - Body

    var body: some View {
        ZStack(alignment: .topLeading) {
            // 主内容滚动视图
            mainContentScrollView

            // 顶部导航栏
            topNavigationBar
        }
        .hideTabBar()
        .navigationBarHidden(true)
        .overlay {
            // 全屏图片查看器
            fullScreenImageViewer
        }
        .sheet(isPresented: $viewModel.isAuthorProfilePresented) {
            // 作者个人资料页面
            authorProfileSheet
        }
        .onAppear {
            setupInitialState()
        }
    }

    // MARK: - Main Content Views

    /// 主内容滚动视图（优化间距）
    private var mainContentScrollView: some View {
        GeometryReader { geometry in
            let safeAreaTop = geometry.safeAreaInsets.top

            ScrollView {
                LazyVStack(spacing: 0, pinnedViews: [.sectionHeaders]) {
                    Section {
                        VStack(alignment: .leading, spacing: 24) {
                            // 内容信息区域
                            contentInfoSection

                            // 旅程路线区域
                            journeyRouteSection

                            // 评论区域
                            commentsSection
                        }
                        .padding(.bottom, 32)
                    } header: {
                        stickyImageHeader(safeAreaTop: safeAreaTop)
                    }
                }
            }
            .ignoresSafeArea(edges: .top)
            .coordinateSpace(name: "scrollView")
        }
    }

    /// 吸顶图片头部
    private func stickyImageHeader(safeAreaTop: CGFloat) -> some View {
        GeometryReader { geometry in
            let frame = geometry.frame(in: .named("scrollView"))
            let offset = frame.minY

            // 计算动态高度：正常380px，最小200px
            let normalHeight: CGFloat = 380
            let minHeight: CGFloat = 200
            let currentHeight = max(minHeight, normalHeight + min(0, offset))

            ZStack(alignment: .bottom) {
                // 图片轮播组件
                ImageCarouselView(
                    imageUrls: cachedImageUrls,
                    currentPage: $viewModel.currentPage,
                    isAutoScrolling: $viewModel.isAutoScrolling,
                    onImageTap: {
                        viewModel.showFullScreenImageViewer()
                    },
                    onZoomChanged: { isZoomed in
                        viewModel.handleZoomChanged(isZoomed)
                    }
                )
                .frame(height: currentHeight)
                .clipped()

                // 底部信息覆盖层
                if currentHeight <= minHeight + 20 {
                    compactCarouselBottomOverlay
                } else {
                    carouselBottomOverlay
                }
            }
            .frame(height: currentHeight)
            .background(Color(.systemBackground))
            .onAppear {
                print("🔍 Sticky Header - offset: \(offset), height: \(currentHeight)")
            }
        }
        .frame(height: 380)
        .ignoresSafeArea()
    }

    // MARK: - Component Views

    /// 图片轮播区域（性能优化）
    private var imageCarouselSection: some View {
        ZStack(alignment: .bottom) {
            // 图片轮播组件 - 使用缓存的URL数组
            ImageCarouselView(
                imageUrls: cachedImageUrls,
                currentPage: $viewModel.currentPage,
                isAutoScrolling: $viewModel.isAutoScrolling,
                onImageTap: {
                    viewModel.showFullScreenImageViewer()
                },
                onZoomChanged: { isZoomed in
                    viewModel.handleZoomChanged(isZoomed)
                }
            )

            // 底部信息覆盖层
            carouselBottomOverlay
        }
        .frame(height: 380)
        .ignoresSafeArea()
    }

    /// 轮播图底部覆盖层（性能优化）
    private var carouselBottomOverlay: some View {
        HStack {
            // 作者信息 - 使用缓存的作者信息
            AuthorInfoView(
                avatarUrl: cachedAuthorInfo.avatarUrl,
                username: cachedAuthorInfo.username,
                onTap: {
                    viewModel.showAuthorProfile()
                }
            )

            Spacer()

            // 页面指示器 - 使用缓存的图片数量
            PageIndicatorView(
                totalPages: cachedImageUrls.count,
                currentPage: viewModel.currentPage
            )
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 16)
    }

    /// 内容信息区域（性能优化）
    private var contentInfoSection: some View {
        VStack(spacing: 0) {
            // 使用缓存的内容信息，避免重复计算
            ContentInfoView(
                title: cachedContentInfo.title,
                description: cachedContentInfo.description,
                location: cachedContentInfo.location,
                topic: cachedContentInfo.topic
            )
            .padding(.top, 24)
            .padding(.bottom, 20)
        }
        .background(
            RoundedRectangle(cornerRadius: 16) // 统一圆角大小为16
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.03), radius: 12, x: 0, y: 4) // 更自然的阴影效果
        )
        .padding(.horizontal, 16)
        .padding(.top, -10) // 与图片重叠效果
    }

    /// 旅程路线区域
    @ViewBuilder
    private var journeyRouteSection: some View {
        if let journey = getJourneyRoute() {
            JourneyRouteCardView(
                journey: journey,
                onViewTap: {
                    viewModel.showJourneyDetail = true
                },
                onForkTap: {
                    // TODO: 实现复制行程功能
                    print("复制行程功能待实现")
                }
            )
            .padding(.horizontal, 16)
            .padding(.top, 8)
        }
    }

    /// 评论区域
    private var commentsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 评论标题
            commentsHeader

            // 评论列表或空状态
            commentsContent

            // 评论输入框
            commentInputSection
        }
        .background(
            RoundedRectangle(cornerRadius: 16) // 统一圆角大小为16
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.03), radius: 12, x: 0, y: 4) // 更自然的阴影效果
        )
        .padding(.horizontal, 16)
    }

    /// 评论标题
    private var commentsHeader: some View {
        HStack {
            Text("评论")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)

            if viewModel.commentCount > 0 {
                Text("(\(viewModel.commentCount))")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
        .padding(.horizontal, 20)
        .padding(.top, 20)
    }

    /// 评论内容区域
    @ViewBuilder
    private var commentsContent: some View {
        if viewModel.comments.isEmpty {
            // 空状态
            VStack(spacing: 12) {
                Image(systemName: "bubble.left.and.bubble.right")
                    .font(.title2)
                    .foregroundColor(.secondary)

                Text("还没有评论，来说点什么吧")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 24)
        } else {
            LazyVStack(spacing: 12) {
                ForEach(viewModel.comments) { comment in
                    CommentView(comment: comment)
                        .padding(.horizontal, 20)
                }
            }
        }
    }

    /// 评论输入区域
    private var commentInputSection: some View {
        HStack(spacing: 12) {
            // 用户头像
            Circle()
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [Color.blue.opacity(0.2), Color.blue.opacity(0.1)]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 36, height: 36)
                .overlay(
                    Image(systemName: "person.fill")
                        .font(.caption)
                        .foregroundColor(.blue)
                )

            // 输入框（调整为适中大小）
            TextField("写下你的想法...", text: $viewModel.commentText)
                .font(.subheadline) // 调整回.subheadline，与作者名称保持一致
                .foregroundColor(.primary)
                .onSubmit {
                    handleCommentSubmission()
                }

            // 发送按钮
            Button(action: handleCommentSubmission) {
                Image(systemName: "paperplane.fill")
                    .font(.subheadline)
                    .foregroundColor(isCommentTextValid ? .blue : .secondary)
            }
            .disabled(!isCommentTextValid)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 24)
                .fill(Color(.systemGray6))
                .overlay(
                    RoundedRectangle(cornerRadius: 24)
                        .stroke(Color(.systemGray4), lineWidth: 0.5)
                )
        )
        .padding(.horizontal, 20)
        .padding(.bottom, 20)
    }

    /// 顶部导航栏
    private var topNavigationBar: some View {
        HStack {
            // 返回按钮
            Button(action: { dismiss() }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.white)
                    .frame(width: 40, height: 40)
                    .background(Color.black.opacity(0.3))
                    .clipShape(Circle())
            }

            Spacer()

            // 分享按钮
            Button(action: { viewModel.handleShare() }) {
                Image(systemName: "square.and.arrow.up")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                    .frame(width: 40, height: 40)
                    .background(Color.black.opacity(0.3))
                    .clipShape(Circle())
            }

            // 点赞按钮
            Button(action: { viewModel.handleLike() }) {
                HStack(spacing: 4) {
                    Image(systemName: viewModel.isLiked ? "heart.fill" : "heart")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(viewModel.isLiked ? .red : .white)
                    Text("\(viewModel.likeCount)")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white)
                }
                .frame(height: 40)
                .padding(.horizontal, 12)
                .background(Color.black.opacity(0.3))
                .clipShape(Capsule())
            }
        }
        .padding(.horizontal, 16)
        .padding(.top, 16)
    }

    /// 全屏图片查看器（性能优化）
    @ViewBuilder
    private var fullScreenImageViewer: some View {
        if viewModel.showFullScreenViewer {
            FullScreenImageViewerView(
                imageUrls: cachedImageUrls, // 使用缓存的图片URL
                currentPage: $viewModel.currentPage,
                isPresented: $viewModel.showFullScreenViewer,
                onZoomChanged: { isZoomed in
                    viewModel.handleZoomChanged(isZoomed)
                }
            )
            .transition(.asymmetric(
                insertion: .scale(scale: 0.3, anchor: .center)
                    .combined(with: .opacity),
                removal: .scale(scale: 0.3, anchor: .center)
                        .combined(with: .opacity)
                ))
                .zIndex(1000)
        }
    }

    /// 作者个人资料页面（性能优化）
    private var authorProfileSheet: some View {
        UserProfileView(
            username: cachedAuthorInfo.username, // 使用缓存的作者信息
            avatarUrl: cachedAuthorInfo.avatarUrl,
            isCurrentUser: false,
            contentTags: getContentTopicAsStringArray()
        )
    }

    // MARK: - Helper Methods

    /// 设置初始状态
    private func setupInitialState() {
        let imageCount = getImageUrls().count
        if viewModel.currentPage >= imageCount {
            viewModel.currentPage = 0
        }
    }

    /// 处理评论提交
    private func handleCommentSubmission() {
        if isCommentTextValid {
            viewModel.handleCommentSubmission()
        }
    }

    /// 检查评论文本是否有效（性能优化：避免重复计算）
    private var isCommentTextValid: Bool {
        !viewModel.commentText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }

    // MARK: - Performance Optimized Computed Properties

    /// 缓存的图片URL数组（避免重复计算）
    private var cachedImageUrls: [String] {
        getImageUrls()
    }

    /// 缓存的作者信息（避免重复计算）
    private var cachedAuthorInfo: (avatarUrl: String, username: String) {
        (getAuthorAvatarUrl(), getAuthorUsername())
    }

    /// 缓存的内容信息（避免重复计算）
    private var cachedContentInfo: (title: String, description: String, location: String, topic: Topic) {
        (getContentTitle(), getContentDescription(), getContentLocation(), getContentTopic())
    }
}

// MARK: - Data Access Extension

extension DiscoverDetailContentView {

    /// 获取图片URL列表（缓存优化）
    /// - Returns: 图片URL数组，优先使用详情数据，其次使用预览数据
    private func getImageUrls() -> [String] {
        // 使用计算属性缓存，避免重复计算
        if let content = viewModel.detailContent {
            return content.images.map { $0.url }
        } else if let previewData = viewModel.previewData {
            return [previewData.coverImage].compactMap { $0 }
        } else {
            return []
        }
    }

    /// 获取作者头像URL（缓存优化）
    /// - Returns: 作者头像URL字符串
    private func getAuthorAvatarUrl() -> String {
        // 使用nil-coalescing优化性能
        if let content = viewModel.detailContent {
            return content.author.avatarUrl ?? ""
        } else if let previewData = viewModel.previewData {
            return previewData.authorAvatar ?? ""
        } else {
            return ""
        }
    }

    /// 获取作者用户名
    /// - Returns: 作者用户名字符串
    private func getAuthorUsername() -> String {
        if let content = viewModel.detailContent {
            return content.author.displayName
        } else if let previewData = viewModel.previewData {
            return previewData.authorName
        } else {
            return ""
        }
    }

    /// 获取内容标题
    /// - Returns: 内容标题字符串
    private func getContentTitle() -> String {
        if let content = viewModel.detailContent {
            return content.title
        } else if let previewData = viewModel.previewData {
            return previewData.title
        } else {
            return ""
        }
    }

    /// 获取内容描述
    /// - Returns: 内容描述字符串，仅在有详情数据时返回
    private func getContentDescription() -> String {
        if let content = viewModel.detailContent {
            return content.description
        } else {
            return ""
        }
    }

    /// 获取位置信息
    /// - Returns: 位置名称字符串
    private func getContentLocation() -> String {
        if let content = viewModel.detailContent {
            return content.location?.name ?? ""
        } else {
            return ""
        }
    }

    /// 获取话题信息
    /// - Returns: 话题对象，如果没有数据则返回默认话题
    private func getContentTopic() -> Topic {
        if let content = viewModel.detailContent {
            return content.topic
        } else {
            // 提供默认话题（这种情况不应该发生，因为发现内容必然有话题）
            return Topic(id: "1", name: "自然风光", order: 1)
        }
    }

    /// 获取话题信息作为字符串数组（用于兼容性）
    /// - Returns: 话题名称数组
    private func getContentTopicAsStringArray() -> [String] {
        let topic = getContentTopic()
        return [topic.name]
    }

    /// 获取旅程路线
    /// - Returns: 旅程路线对象，如果没有则返回nil
    private func getJourneyRoute() -> DiscoverDetailModels.JourneyRoute? {
        return viewModel.detailContent?.journey
    }
}

// MARK: - Preview

#Preview {
    DiscoverDetailContentView(
        navigationParams: DiscoverDetailModels.NavigationParams(
            id: "preview_001",
            previewData: DiscoverDetailModels.PreviewData(
                title: "美丽的自然风光探索之旅",
                coverImage: "https://c-ssl.dtstatic.com/uploads/blog/202408/14/9WSP7q6eh8wwMP6.thumb.1000_0.jpg",
                authorName: "旅行达人",
                authorAvatar: "https://img1.baidu.com/it/u=1747081318,2650263390&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500"
            )
        )
    )
}
