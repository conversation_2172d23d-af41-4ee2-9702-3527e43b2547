import SwiftUI

// MARK: - Scale Button Style

/// 缩放按钮样式
/// 提供按下时的缩放动画效果，增强用户交互反馈
struct ScaleButtonStyle: ButtonStyle {
    
    // MARK: - Properties
    
    /// 按下时的缩放比例
    let pressedScale: CGFloat
    /// 动画持续时间
    let animationDuration: Double
    /// 是否启用触觉反馈
    let hapticFeedback: Bool
    
    // MARK: - Initializer
    
    /// 初始化缩放按钮样式
    /// - Parameters:
    ///   - pressedScale: 按下时的缩放比例，默认0.95
    ///   - animationDuration: 动画持续时间，默认0.1秒
    ///   - hapticFeedback: 是否启用触觉反馈，默认true
    init(
        pressedScale: CGFloat = 0.95,
        animationDuration: Double = 0.1,
        hapticFeedback: Bool = true
    ) {
        self.pressedScale = pressedScale
        self.animationDuration = animationDuration
        self.hapticFeedback = hapticFeedback
    }
    
    // MARK: - ButtonStyle Implementation
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? pressedScale : 1.0)
            .opacity(configuration.isPressed ? 0.8 : 1.0)
            .animation(
                .easeInOut(duration: animationDuration),
                value: configuration.isPressed
            )
            .onChange(of: configuration.isPressed) { _, isPressed in
                if isPressed && hapticFeedback {
                    // 轻微触觉反馈
                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                    impactFeedback.impactOccurred()
                }
            }
    }
}

// MARK: - Bounce Button Style

/// 弹跳按钮样式
/// 提供按下时的弹跳动画效果
struct BounceButtonStyle: ButtonStyle {
    
    // MARK: - Properties
    
    /// 弹跳缩放比例
    let bounceScale: CGFloat
    /// 动画持续时间
    let animationDuration: Double
    /// 弹跳阻尼
    let dampingFraction: CGFloat
    
    // MARK: - Initializer
    
    /// 初始化弹跳按钮样式
    /// - Parameters:
    ///   - bounceScale: 弹跳缩放比例，默认1.1
    ///   - animationDuration: 动画持续时间，默认0.3秒
    ///   - dampingFraction: 弹跳阻尼，默认0.6
    init(
        bounceScale: CGFloat = 1.1,
        animationDuration: Double = 0.3,
        dampingFraction: CGFloat = 0.6
    ) {
        self.bounceScale = bounceScale
        self.animationDuration = animationDuration
        self.dampingFraction = dampingFraction
    }
    
    // MARK: - ButtonStyle Implementation
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? bounceScale : 1.0)
            .animation(
                .spring(
                    response: animationDuration,
                    dampingFraction: dampingFraction,
                    blendDuration: 0
                ),
                value: configuration.isPressed
            )
    }
}

// MARK: - Glow Button Style

/// 发光按钮样式
/// 提供按下时的发光效果
struct GlowButtonStyle: ButtonStyle {
    
    // MARK: - Properties
    
    /// 发光颜色
    let glowColor: Color
    /// 发光半径
    let glowRadius: CGFloat
    /// 动画持续时间
    let animationDuration: Double
    
    // MARK: - Initializer
    
    /// 初始化发光按钮样式
    /// - Parameters:
    ///   - glowColor: 发光颜色，默认蓝色
    ///   - glowRadius: 发光半径，默认8
    ///   - animationDuration: 动画持续时间，默认0.2秒
    init(
        glowColor: Color = .blue,
        glowRadius: CGFloat = 8,
        animationDuration: Double = 0.2
    ) {
        self.glowColor = glowColor
        self.glowRadius = glowRadius
        self.animationDuration = animationDuration
    }
    
    // MARK: - ButtonStyle Implementation
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .shadow(
                color: configuration.isPressed ? glowColor.opacity(0.6) : .clear,
                radius: configuration.isPressed ? glowRadius : 0
            )
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .animation(
                .easeInOut(duration: animationDuration),
                value: configuration.isPressed
            )
    }
}

// MARK: - Press Button Style

/// 按压按钮样式
/// 提供按下时的按压效果，类似物理按钮
struct PressButtonStyle: ButtonStyle {
    
    // MARK: - Properties
    
    /// 按压偏移量
    let pressOffset: CGFloat
    /// 阴影颜色
    let shadowColor: Color
    /// 动画持续时间
    let animationDuration: Double
    
    // MARK: - Initializer
    
    /// 初始化按压按钮样式
    /// - Parameters:
    ///   - pressOffset: 按压偏移量，默认2
    ///   - shadowColor: 阴影颜色，默认黑色
    ///   - animationDuration: 动画持续时间，默认0.1秒
    init(
        pressOffset: CGFloat = 2,
        shadowColor: Color = .black.opacity(0.2),
        animationDuration: Double = 0.1
    ) {
        self.pressOffset = pressOffset
        self.shadowColor = shadowColor
        self.animationDuration = animationDuration
    }
    
    // MARK: - ButtonStyle Implementation
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .offset(y: configuration.isPressed ? pressOffset : 0)
            .shadow(
                color: shadowColor,
                radius: configuration.isPressed ? 2 : 4,
                x: 0,
                y: configuration.isPressed ? 1 : 3
            )
            .animation(
                .easeInOut(duration: animationDuration),
                value: configuration.isPressed
            )
    }
}

// MARK: - Button Style Extensions

extension ButtonStyle where Self == ScaleButtonStyle {
    
    /// 缩放按钮样式的便捷访问
    static var scale: ScaleButtonStyle {
        ScaleButtonStyle()
    }
    
    /// 自定义缩放按钮样式
    static func scale(
        pressedScale: CGFloat = 0.95,
        animationDuration: Double = 0.1,
        hapticFeedback: Bool = true
    ) -> ScaleButtonStyle {
        ScaleButtonStyle(
            pressedScale: pressedScale,
            animationDuration: animationDuration,
            hapticFeedback: hapticFeedback
        )
    }
}

extension ButtonStyle where Self == BounceButtonStyle {
    
    /// 弹跳按钮样式的便捷访问
    static var bounce: BounceButtonStyle {
        BounceButtonStyle()
    }
}

extension ButtonStyle where Self == GlowButtonStyle {
    
    /// 发光按钮样式的便捷访问
    static var glow: GlowButtonStyle {
        GlowButtonStyle()
    }
}

extension ButtonStyle where Self == PressButtonStyle {
    
    /// 按压按钮样式的便捷访问
    static var press: PressButtonStyle {
        PressButtonStyle()
    }
}

// MARK: - Preview

#Preview("Button Styles") {
    VStack(spacing: 20) {
        Button("Scale Button") {
            print("Scale button tapped")
        }
        .buttonStyle(.scale)
        .padding()
        .background(Color.blue)
        .foregroundColor(.white)
        .cornerRadius(8)
        
        Button("Bounce Button") {
            print("Bounce button tapped")
        }
        .buttonStyle(.bounce)
        .padding()
        .background(Color.green)
        .foregroundColor(.white)
        .cornerRadius(8)
        
        Button("Glow Button") {
            print("Glow button tapped")
        }
        .buttonStyle(.glow)
        .padding()
        .background(Color.purple)
        .foregroundColor(.white)
        .cornerRadius(8)
        
        Button("Press Button") {
            print("Press button tapped")
        }
        .buttonStyle(.press)
        .padding()
        .background(Color.orange)
        .foregroundColor(.white)
        .cornerRadius(8)
    }
    .padding()
}
