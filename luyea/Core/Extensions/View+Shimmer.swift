import SwiftUI

// MARK: - Shimmer Effect Extension

/// 微光效果扩展
/// 为视图添加优雅的加载动画效果
extension View {
    
    /// 添加微光效果
    /// - Parameters:
    ///   - active: 是否激活微光效果
    ///   - duration: 动画持续时间
    ///   - bounce: 是否反弹
    /// - Returns: 带有微光效果的视图
    func shimmer(
        active: Bool = true,
        duration: Double = 1.5,
        bounce: Bool = false
    ) -> some View {
        modifier(ShimmerModifier(
            active: active,
            duration: duration,
            bounce: bounce
        ))
    }
}

// MARK: - Shimmer Modifier

/// 微光效果修饰器
private struct ShimmerModifier: ViewModifier {
    
    // MARK: - Properties
    
    let active: Bool
    let duration: Double
    let bounce: Bool
    
    @State private var phase: CGFloat = 0
    
    // MARK: - Body
    
    func body(content: Content) -> some View {
        content
            .overlay(
                shimmerOverlay
                    .opacity(active ? 1 : 0)
            )
            .onAppear {
                if active {
                    startAnimation()
                }
            }
    }
    
    // MARK: - Private Views
    
    /// 微光覆盖层
    private var shimmerOverlay: some View {
        GeometryReader { geometry in
            let width = geometry.size.width
            let height = geometry.size.height
            
            LinearGradient(
                gradient: Gradient(stops: [
                    .init(color: Color.clear, location: 0),
                    .init(color: Color.white.opacity(0.3), location: 0.5),
                    .init(color: Color.clear, location: 1)
                ]),
                startPoint: .leading,
                endPoint: .trailing
            )
            .frame(width: width * 2)
            .offset(x: width * (phase - 1))
            .mask(
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.black.opacity(0.3),
                                Color.black,
                                Color.black.opacity(0.3)
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
            )
        }
        .allowsHitTesting(false)
    }
    
    // MARK: - Private Methods
    
    /// 开始动画
    private func startAnimation() {
        withAnimation(
            .linear(duration: duration)
            .repeatForever(autoreverses: bounce)
        ) {
            phase = 1
        }
    }
}

// MARK: - Skeleton View Extension

extension View {
    
    /// 添加骨架屏效果
    /// - Parameters:
    ///   - active: 是否激活骨架屏
    ///   - animation: 动画类型
    /// - Returns: 带有骨架屏效果的视图
    func skeleton(
        active: Bool = true,
        animation: Animation = .easeInOut(duration: 1.0).repeatForever(autoreverses: true)
    ) -> some View {
        modifier(SkeletonModifier(active: active, animation: animation))
    }
}

// MARK: - Skeleton Modifier

/// 骨架屏修饰器
private struct SkeletonModifier: ViewModifier {
    
    let active: Bool
    let animation: Animation
    
    @State private var opacity: Double = 0.3
    
    func body(content: Content) -> some View {
        content
            .opacity(active ? opacity : 1.0)
            .overlay(
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color.gray.opacity(active ? 0.3 : 0))
                    .opacity(active ? opacity : 0)
            )
            .onAppear {
                if active {
                    withAnimation(animation) {
                        opacity = 0.7
                    }
                }
            }
            .onChange(of: active) { _, newValue in
                if newValue {
                    withAnimation(animation) {
                        opacity = 0.7
                    }
                } else {
                    opacity = 1.0
                }
            }
    }
}

// MARK: - Pulse Effect Extension

extension View {
    
    /// 添加脉冲效果
    /// - Parameters:
    ///   - active: 是否激活脉冲效果
    ///   - scale: 缩放比例
    ///   - duration: 动画持续时间
    /// - Returns: 带有脉冲效果的视图
    func pulse(
        active: Bool = true,
        scale: CGFloat = 1.05,
        duration: Double = 1.0
    ) -> some View {
        modifier(PulseModifier(
            active: active,
            scale: scale,
            duration: duration
        ))
    }
}

// MARK: - Pulse Modifier

/// 脉冲效果修饰器
private struct PulseModifier: ViewModifier {
    
    let active: Bool
    let scale: CGFloat
    let duration: Double
    
    @State private var isAnimating = false
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(active && isAnimating ? scale : 1.0)
            .onAppear {
                if active {
                    startPulse()
                }
            }
            .onChange(of: active) { _, newValue in
                if newValue {
                    startPulse()
                } else {
                    isAnimating = false
                }
            }
    }
    
    private func startPulse() {
        withAnimation(
            .easeInOut(duration: duration)
            .repeatForever(autoreverses: true)
        ) {
            isAnimating = true
        }
    }
}

// MARK: - Preview

#Preview("Shimmer Effects") {
    VStack(spacing: 20) {
        // 微光效果示例
        RoundedRectangle(cornerRadius: 12)
            .fill(Color.gray.opacity(0.2))
            .frame(height: 60)
            .shimmer()
        
        // 骨架屏效果示例
        VStack(alignment: .leading, spacing: 8) {
            Rectangle()
                .frame(height: 20)
                .skeleton()
            
            Rectangle()
                .frame(width: 200, height: 20)
                .skeleton()
        }
        
        // 脉冲效果示例
        Circle()
            .fill(Color.blue)
            .frame(width: 60, height: 60)
            .pulse()
    }
    .padding()
}
